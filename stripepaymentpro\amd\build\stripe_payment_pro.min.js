define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=t(e);const{call:n}=o.default,s=e=>{const t=new Map;return{getelement(o){const n=`${o}-${e}`;return t.has(n)||t.set(n,document.getElementById(n)),t.get(n)},setelement(e,t){const o=this.getelement(e);o&&(o.innerHTML=t)},toggleelement(e,t){const o=this.getelement(e);o&&(o.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,o,n=(t?"0.7":"1")){const s=this.getelement(e);s&&(s.disabled=t,s.textContent=o,s.style.opacity=n,s.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment_pro:function(e,t,o,r,a,c,l){const i=s(o);if(void 0===window.Stripe)return;const u=(e,t,o)=>{let n;switch(o){case"error":n="red";break;case"success":n="green";break;default:n="blue"}i.setelement(e,`<p style="color: ${n}; font-weight: bold;">${t}</p>`),i.toggleelement(e,!0)},m=e=>{i.setelement(e,""),i.toggleelement(e,!1)};[{id:"apply",event:"click",handler:async e=>{e.preventDefault();const s=i.getelement("coupon"),r=s?.value.trim();if(!r)return u("showmessage",a,"error"),void i.focuselement("coupon");i.setbutton("apply",!0,c);try{const e=await((e,t)=>n([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(r,o);if(void 0===e?.status)throw new Error("Invalid server response");t=r,i.toggleelement("coupon",!1),i.toggleelement("apply",!1),(e=>{if(e.message?u("showmessage",e.message,"error"===e.uistate?"error":"success"):m("showmessage"),i.toggleelement("enrolbutton","paid"===e.uistate),i.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(i.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection&&(e.couponname&&i.setelement("discounttag",e.couponname),e.discountamount&&e.currency&&i.setelement("discountamountdisplay",`-${e.currency} ${e.discountamount}`),e.discountamount&&e.discountvalue)){const t="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${e.currency} ${e.discountvalue} off`;i.setelement("discountnote",t)}if(e.status&&e.currency){const t=i.getelement("totalamount");t&&(t.textContent=`${e.currency} ${parseFloat(e.status).toFixed(2)}`)}}})(e)}catch(e){u("showmessage",e.message||"Coupon validation failed","error"),i.focuselement("coupon")}}},{id:"enrolbutton",event:"click",handler:async()=>{if(i.getelement("enrolbutton")){m("paymentresponse"),i.setbutton("enrolbutton",!0,r);try{const s=await((e,t,o)=>n([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:o}}])[0])(e,t,o);s.error?.message?u("paymentresponse",s.error.message,"error"):"success"===s.status&&s.redirecturl?window.location.href=s.redirecturl:u("paymentresponse","Unknown error occurred during payment.","error")}catch(e){u("paymentresponse",e.message,"error")}finally{i.toggleelement("enrolbutton",!1)}}}}].forEach(({id:e,event:t,handler:o})=>{const n=i.getelement(e);n&&n.addEventListener(t,o)})},deactivateCoupon:(e,t)=>n([{methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:e,couponid:t}}])[0],deactivateAllCoupons:e=>n([{methodname:"moodle_stripepaymentpro_deactivate_all_coupons",args:{courseid:e}}])[0]}});
